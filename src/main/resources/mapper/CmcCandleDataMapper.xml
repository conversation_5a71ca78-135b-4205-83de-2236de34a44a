<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trading.financialindicatordaemon.mapper.CmcCandleDataMapper">

    <insert id="insert">
        INSERT INTO crypto_data.cmc_candle_data (symbol,
        conversion_currency,
        time_open,
        time_close,
        time_high,
        time_low,
        quote)
        VALUES
        <foreach collection="quotes" item="quote" separator=",">
            (#{cryptoCurrencySymbol},
            #{conversionCurrency},
            #{quote.timeOpen}::timestamp,
            #{quote.timeClose}::timestamp,
            #{quote.timeHigh}::timestamp,
            #{quote.timeLow}::timestamp,
            #{quote.quote})
        </foreach>
        ON CONFLICT (symbol, conversion_currency, time_close) DO NOTHING;
    </insert>

    <select id="findBySymbolAndConversionCurrency"
            resultType="com.trading.financialindicatordaemon.client.CryptoCandleHistoricalQuote">
        SELECT *
        FROM crypto_data.cmc_candle_data
        WHERE symbol = #{cryptoCurrencySymbol}
          AND conversion_currency = #{conversionCurrency}
        ORDER BY time_close
    </select>

    <select id="findLatestCloseTimestamp" resultType="java.time.LocalDateTime">
        SELECT MAX(time_close) AS latest_close_timestamp
        FROM crypto_data.cmc_candle_data
        WHERE symbol = #{symbol}
          AND conversion_currency = #{conversionCurrency}
    </select>
</mapper>
