package com.trading.financialindicatordaemon.mapper;

import com.trading.financialindicatordaemon.client.IndicatorData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IndicatorDataMapper {

    IndicatorDataWrapper findLatestByCoinAndCurrency(@Param("symbol") String symbol,
                                                     @Param("conversionCurrency") String conversionCurrency);

    void insert(@Param("symbol") String symbol,
                @Param("conversionCurrency") String conversionCurrency,
                @Param("indicatorData") List<IndicatorData> indicatorData);
}
