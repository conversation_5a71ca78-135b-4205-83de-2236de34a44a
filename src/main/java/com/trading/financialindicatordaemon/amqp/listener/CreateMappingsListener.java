package com.trading.financialindicatordaemon.amqp.listener;

import com.trading.financialindicatordaemon.amqp.SymbolsMessage;
import com.trading.financialindicatordaemon.amqp.publisher.RabbitMqPublisher;
import com.trading.financialindicatordaemon.config.RabbitMqConfig;
import com.trading.financialindicatordaemon.service.DataMiningService;
import com.trading.financialindicatordaemon.service.DataTransformationService;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class CreateMappingsListener extends BaseRabbitMqListener {

    private final RabbitMqPublisher rabbitMqPublisher;

    public CreateMappingsListener(DataMiningService dataMiningService,
                                  DataTransformationService dataTransformationService,
                                  RabbitMqPublisher rabbitMqPublisher) {
        super(dataMiningService, dataTransformationService);
        this.rabbitMqPublisher = rabbitMqPublisher;
    }

    @RabbitListener(queues = RabbitMqConfig.CREATE_MAPPINGS)
    public void handleCreateMappings(@Payload Map<String, Object> ignoredMessage,
                                     Channel channel,
                                     @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                     Message amqpMessage) {
        handleSimpleMessage("create_mappings", channel, deliveryTag, amqpMessage,
                () -> {
                    dataMiningService.mineMappings();
                    rabbitMqPublisher.mineUsdDataBySymbols(new SymbolsMessage(List.of("ETH")));
                });
    }

}
