package com.trading.financialindicatordaemon.amqp.listener;

import com.trading.financialindicatordaemon.config.RabbitMqConfig;
import com.trading.financialindicatordaemon.service.DataMiningService;
import com.trading.financialindicatordaemon.service.DataTransformationService;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class CreateStatisticsListener extends BaseRabbitMqListener {

    public CreateStatisticsListener(DataMiningService dataMiningService,
                                    DataTransformationService dataTransformationService) {
        super(dataMiningService, dataTransformationService);
    }

    @RabbitListener(queues = RabbitMqConfig.CREATE_STATISTICS)
    public void handleCreateStatistics(@Payload Map<String, Object> ignoredMessage,
                                       Channel channel,
                                       @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                       Message amqpMessage) {
        handleSimpleMessage("create_statistics", channel, deliveryTag, amqpMessage,
                dataTransformationService::createStatistics);
    }
}
