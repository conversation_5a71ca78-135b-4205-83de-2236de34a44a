package com.trading.financialindicatordaemon.amqp.listener;

import com.trading.financialindicatordaemon.config.RabbitMqConfig;
import com.trading.financialindicatordaemon.amqp.SymbolsMessage;
import com.trading.financialindicatordaemon.service.DataMiningService;
import com.trading.financialindicatordaemon.service.DataTransformationService;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Component
public class MineBtcDataListener extends BaseRabbitMqListener {

    public MineBtcDataListener(DataMiningService dataMiningService,
                               DataTransformationService dataTransformationService) {
        super(dataMiningService, dataTransformationService);
    }

    @RabbitListener(queues = RabbitMqConfig.MINE_BTC_DATA_BY_SYMBOLS)
    public void handleMineBtcDataBySymbols(@Payload SymbolsMessage message,
                                           Channel channel,
                                           @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                           Message amqpMessage) {
        handleSymbolsMessage(message, "btc", channel, deliveryTag, amqpMessage);
    }
}

