package com.trading.financialindicatordaemon.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Cryptocurrency statistics response containing USD and BTC data")
public class CryptoStatisticsResponse {

    @Schema(description = "USD-based cryptocurrency statistics")
    private List<CryptoStatistic> usd;

    @Schema(description = "BTC-based cryptocurrency statistics")
    private List<CryptoStatistic> btc;

    public CryptoStatisticsResponse() {}

    public CryptoStatisticsResponse(List<CryptoStatistic> usd, List<CryptoStatistic> btc) {
        this.usd = usd;
        this.btc = btc;
    }

    public List<CryptoStatistic> getUsd() {
        return usd;
    }

    public void setUsd(List<CryptoStatistic> usd) {
        this.usd = usd;
    }

    public List<CryptoStatistic> getBtc() {
        return btc;
    }

    public void setBtc(List<CryptoStatistic> btc) {
        this.btc = btc;
    }
}
