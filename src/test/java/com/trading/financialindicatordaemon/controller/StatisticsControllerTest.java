package com.trading.financialindicatordaemon.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.client.IndicatorData;
import com.trading.financialindicatordaemon.mapper.IndicatorDataWrapper;
import com.trading.financialindicatordaemon.service.IndicatorDataService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.List;

import static org.hamcrest.Matchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(StatisticsController.class)
@DisplayName("Statistics Controller Tests")
class StatisticsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private IndicatorDataService indicatorDataService;

    @Nested
    @DisplayName("GET /api/v1/crypto/statistics")
    class GetCryptoStatistics {

        @Test
        @DisplayName("Should return 200 with empty list when no statistics exist")
        void shouldReturnEmptyListWhenNoStatistics() throws Exception {
            // Given
            when(indicatorDataService.findAll()).thenReturn(List.of());

            // When & Then
            mockMvc.perform(get("/api/v1/crypto/statistics")
                            .contentType(MediaType.APPLICATION_JSON))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$", hasSize(0)));

            verify(indicatorDataService, times(1)).findAll();
        }

        @Test
        @DisplayName("Should return 200 with statistics data when data exists")
        void shouldReturnStatisticsWhenDataExists() throws Exception {
            // Given
            List<IndicatorDataWrapper> mockData = createMockStatisticsData();
            when(indicatorDataService.findAll()).thenReturn(mockData);

            // When & Then
            mockMvc.perform(get("/api/v1/crypto/statistics")
                            .contentType(MediaType.APPLICATION_JSON))
                    .andDo(print())
                    .andExpected(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$", hasSize(2)))
                    .andExpect(jsonPath("$[0].symbol", is("BTC")))
                    .andExpect(jsonPath("$[0].conversionCurrency", is("USD")))
                    .andExpect(jsonPath("$[0].indicatorValues", hasSize(1)))
                    .andExpect(jsonPath("$[0].indicatorValues[0].name", is("Bitcoin")))
                    .andExpect(jsonPath("$[0].indicatorValues[0].close", is(50000.0)))
                    .andExpect(jsonPath("$[1].symbol", is("ETH")))
                    .andExpect(jsonPath("$[1].conversionCurrency", is("BTC")))
                    .andExpect(jsonPath("$[1].indicatorValues", hasSize(1)))
                    .andExpect(jsonPath("$[1].indicatorValues[0].name", is("Ethereum")))
                    .andExpect(jsonPath("$[1].indicatorValues[0].close", is(3000.0)));

            verify(indicatorDataService, times(1)).findAll();
        }

        @Test
        @DisplayName("Should return 500 when service throws exception")
        void shouldReturn500WhenServiceThrowsException() throws Exception {
            // Given
            when(indicatorDataService.findAll()).thenThrow(new RuntimeException("Database connection failed"));

            // When & Then
            mockMvc.perform(get("/api/v1/crypto/statistics")
                            .contentType(MediaType.APPLICATION_JSON))
                    .andDo(print())
                    .andExpect(status().isInternalServerError());

            verify(indicatorDataService, times(1)).findAll();
        }

        @Test
        @DisplayName("Should handle large datasets efficiently")
        void shouldHandleLargeDatasets() throws Exception {
            // Given
            List<IndicatorDataWrapper> largeDataset = createLargeDataset(100);
            when(indicatorDataService.findAll()).thenReturn(largeDataset);

            // When & Then
            mockMvc.perform(get("/api/v1/crypto/statistics")
                            .contentType(MediaType.APPLICATION_JSON))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$", hasSize(100)));

            verify(indicatorDataService, times(1)).findAll();
        }

        @Test
        @DisplayName("Should return proper OpenAPI documentation headers")
        void shouldReturnProperHeaders() throws Exception {
            // Given
            when(indicatorDataService.findAll()).thenReturn(List.of());

            // When & Then
            mockMvc.perform(get("/api/v1/crypto/statistics"))
                    .andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(header().string("Content-Type", "application/json"));

            verify(indicatorDataService, times(1)).findAll();
        }
    }

    private List<IndicatorDataWrapper> createMockStatisticsData() {
        IndicatorDataWrapper btcUsd = new IndicatorDataWrapper();
        btcUsd.setIndicatorValues(List.of(createIndicatorData("Bitcoin", BigDecimal.valueOf(50000))));

        IndicatorDataWrapper ethBtc = new IndicatorDataWrapper();
        ethBtc.setIndicatorValues(List.of(createIndicatorData("Ethereum", BigDecimal.valueOf(3000))));

        return List.of(btcUsd, ethBtc);
    }

    private IndicatorData createIndicatorData(String name, BigDecimal close) {
        IndicatorData data = new IndicatorData();
        data.setName(name);
        data.setClose(close);
        data.setOpen(close.multiply(BigDecimal.valueOf(0.98)));
        data.setHigh(close.multiply(BigDecimal.valueOf(1.05)));
        data.setLow(close.multiply(BigDecimal.valueOf(0.95)));
        data.setVolume(BigDecimal.valueOf(1000000));
        data.setMarketCap(BigDecimal.valueOf(1000000000));
        data.setTimestamp("2024-01-01T00:00:00Z");
        data.setHl2(close.multiply(BigDecimal.valueOf(1.025)));
        data.setSmma15(close.multiply(BigDecimal.valueOf(0.99)));
        data.setSmma19(close.multiply(BigDecimal.valueOf(0.995)));
        data.setSmma25(close.multiply(BigDecimal.valueOf(1.01)));
        data.setSmma29(close.multiply(BigDecimal.valueOf(1.005)));
        data.setP1(true);
        data.setP2(false);
        data.setP3(true);
        data.setColor("green");
        return data;
    }

    private List<IndicatorDataWrapper> createLargeDataset(int size) {
        return java.util.stream.IntStream.range(0, size)
                .mapToObj(i -> {
                    IndicatorDataWrapper wrapper = new IndicatorDataWrapper();
                    wrapper.setIndicatorValues(List.of(
                            createIndicatorData("Crypto" + i, BigDecimal.valueOf(1000 + i))
                    ));
                    return wrapper;
                })
                .toList();
    }
}
