package com.trading.financialindicatordaemon.controller;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.client.IndicatorData;
import com.trading.financialindicatordaemon.service.IndicatorDataService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("Statistics Controller Integration Tests")
class StatisticsControllerIntegrationTest extends BaseTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private IndicatorDataService indicatorDataService;

    @Test
    @DisplayName("Should return statistics from actual database")
    void shouldReturnStatisticsFromDatabase() throws Exception {
        // Given - Insert test data
        insertTestIndicatorData();

        // When & Then
        mockMvc.perform(get("/api/v1/crypto/statistics")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(greaterThanOrEqualTo(0))));
    }

    @Test
    @DisplayName("Should handle concurrent requests properly")
    void shouldHandleConcurrentRequests() throws Exception {
        // Given
        insertTestIndicatorData();

        // When & Then - Simulate concurrent requests
        for (int i = 0; i < 5; i++) {
            mockMvc.perform(get("/api/v1/crypto/statistics")
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON));
        }
    }

    @Test
    @DisplayName("Should return consistent results across multiple calls")
    void shouldReturnConsistentResults() throws Exception {
        // Given
        insertTestIndicatorData();

        // When - Make multiple calls
        String firstResponse = mockMvc.perform(get("/api/v1/crypto/statistics"))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();

        String secondResponse = mockMvc.perform(get("/api/v1/crypto/statistics"))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();

        // Then - Results should be identical
        assert firstResponse.equals(secondResponse);
    }

    @Test
    @DisplayName("Should validate OpenAPI schema compliance")
    void shouldValidateOpenApiSchemaCompliance() throws Exception {
        // Given
        insertTestIndicatorData();

        // When & Then
        mockMvc.perform(get("/api/v1/crypto/statistics"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[*].symbol").exists())
                .andExpect(jsonPath("$[*].conversionCurrency").exists())
                .andExpect(jsonPath("$[*].indicatorValues").exists())
                .andExpect(jsonPath("$[*].indicatorValues").isArray());
    }

    @Test
    @DisplayName("Should handle database transaction rollback properly")
    void shouldHandleDatabaseTransactionRollback() throws Exception {
        // Given - Data inserted in transaction will be rolled back after test
        insertTestIndicatorData();

        // When & Then
        mockMvc.perform(get("/api/v1/crypto/statistics"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        // Transaction will rollback automatically due to @Transactional
    }

    private void insertTestIndicatorData() {
        try {
            // Create test indicator data
            List<IndicatorData> testData = createTestIndicatorData();
            
            // Insert via service (this would normally go through the mapper)
            indicatorDataService.insert("BTC", "USD", testData);
            indicatorDataService.insert("ETH", "BTC", testData);
        } catch (Exception e) {
            // Handle gracefully - some tests may not have full database setup
            System.out.println("Could not insert test data: " + e.getMessage());
        }
    }

    private List<IndicatorData> createTestIndicatorData() {
        IndicatorData data = new IndicatorData();
        data.setName("Test Crypto");
        data.setClose(BigDecimal.valueOf(50000));
        data.setOpen(BigDecimal.valueOf(49000));
        data.setHigh(BigDecimal.valueOf(52000));
        data.setLow(BigDecimal.valueOf(48000));
        data.setVolume(BigDecimal.valueOf(1000000));
        data.setMarketCap(BigDecimal.valueOf(1000000000));
        data.setTimestamp("2024-01-01T00:00:00Z");
        data.setHl2(BigDecimal.valueOf(50000));
        data.setSmma15(BigDecimal.valueOf(49500));
        data.setSmma19(BigDecimal.valueOf(49750));
        data.setSmma25(BigDecimal.valueOf(50250));
        data.setSmma29(BigDecimal.valueOf(50125));
        data.setP1(true);
        data.setP2(false);
        data.setP3(true);
        data.setColor("green");
        
        return List.of(data);
    }
}
