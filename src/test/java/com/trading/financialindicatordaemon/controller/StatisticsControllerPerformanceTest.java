package com.trading.financialindicatordaemon.controller;

import com.trading.financialindicatordaemon.BaseTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("Statistics Controller Performance Tests")
class StatisticsControllerPerformanceTest extends BaseTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    @DisplayName("Should respond within 5 seconds under normal load")
    @Timeout(value = 5, unit = TimeUnit.SECONDS)
    void shouldRespondWithinTimeLimit() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/crypto/statistics")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Should handle 50 concurrent requests efficiently")
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    void shouldHandleConcurrentRequests() throws Exception {
        // Given
        int numberOfRequests = 50;

        // When
        CompletableFuture<Void>[] futures = IntStream.range(0, numberOfRequests)
                .mapToObj(i -> CompletableFuture.runAsync(() -> {
                    try {
                        mockMvc.perform(get("/api/v1/crypto/statistics")
                                        .contentType(MediaType.APPLICATION_JSON))
                                .andExpect(status().isOk());
                    } catch (Exception e) {
                        throw new RuntimeException("Request failed", e);
                    }
                }))
                .toArray(CompletableFuture[]::new);

        // Then
        CompletableFuture.allOf(futures).join();
    }

    @Test
    @DisplayName("Should maintain consistent response times across multiple calls")
    @Timeout(value = 15, unit = TimeUnit.SECONDS)
    void shouldMaintainConsistentResponseTimes() throws Exception {
        // Given
        int numberOfCalls = 10;
        long[] responseTimes = new long[numberOfCalls];

        // When
        for (int i = 0; i < numberOfCalls; i++) {
            long startTime = System.currentTimeMillis();
            
            mockMvc.perform(get("/api/v1/crypto/statistics")
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
            
            responseTimes[i] = System.currentTimeMillis() - startTime;
        }

        // Then - Calculate average and ensure no response takes more than 2x average
        double average = IntStream.range(0, numberOfCalls)
                .mapToLong(i -> responseTimes[i])
                .average()
                .orElse(0);

        for (long responseTime : responseTimes) {
            assert responseTime <= (average * 2) : 
                String.format("Response time %d ms exceeds 2x average %f ms", responseTime, average);
        }
    }

    @Test
    @DisplayName("Should handle rapid sequential requests without degradation")
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void shouldHandleRapidSequentialRequests() throws Exception {
        // When & Then
        for (int i = 0; i < 20; i++) {
            mockMvc.perform(get("/api/v1/crypto/statistics")
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
        }
    }

    @Test
    @DisplayName("Should not cause memory leaks during repeated calls")
    @Timeout(value = 20, unit = TimeUnit.SECONDS)
    void shouldNotCauseMemoryLeaks() throws Exception {
        // Given
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();

        // When - Make many requests
        for (int i = 0; i < 100; i++) {
            mockMvc.perform(get("/api/v1/crypto/statistics")
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
            
            // Suggest garbage collection every 20 requests
            if (i % 20 == 0) {
                System.gc();
                Thread.sleep(10);
            }
        }

        // Then - Memory usage should not have grown significantly
        System.gc();
        Thread.sleep(100);
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = finalMemory - initialMemory;
        
        // Allow for some memory increase but not excessive (50MB threshold)
        assert memoryIncrease < 50 * 1024 * 1024 : 
            String.format("Memory increased by %d bytes, which may indicate a memory leak", memoryIncrease);
    }
}
