package com.trading.financialindicatordaemon.service;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.client.CryptoCandleHistoricalQuote;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class DataMiningServiceTest extends BaseTest {

    @Autowired
    private DataMiningService dataMiningService;
    @Autowired
    private CmcMappingsMappingService cmcMappingsMappingService;
    @Autowired
    private CmcCandleDataService cmcCandleDataService;

    @Test
    public void findMappings_shouldStore() {
        assertTrue(cmcMappingsMappingService.findMappings("BTC").isEmpty());
        dataMiningService.mineMappings();
        assertTrue(cmcMappingsMappingService.findMappings("BTC").isPresent());
    }

    // mineSymbols
    @Test
    public void mineSymbols_shouldStore() {
        dataMiningService.mineMappings();
        dataMiningService.mineSymbols(List.of("SOL"), 2781);
        List<CryptoCandleHistoricalQuote> cryptoCandleHistoricalQuotes = cmcCandleDataService.find("SOL", "USD");
        assertThat(cryptoCandleHistoricalQuotes).isNotEmpty();
        assertThat(cryptoCandleHistoricalQuotes).hasSize(274);
    }

}
