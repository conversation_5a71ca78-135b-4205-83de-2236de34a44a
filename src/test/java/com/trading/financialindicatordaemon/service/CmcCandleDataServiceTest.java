package com.trading.financialindicatordaemon.service;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.client.CryptoCandleHistoricalQuote;
import com.trading.financialindicatordaemon.client.CryptoCandleHistoricalQuote.Quote;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

public class CmcCandleDataServiceTest extends BaseTest {

    @Autowired
    private CmcCandleDataService cmcCandleDataService;

    @Test
    public void insert_duplicatesAreNotInserted() {
        assertThat(cmcCandleDataService.find("BTC", "USD")).size().isEqualTo(0);
        CryptoCandleHistoricalQuote quote1 = new CryptoCandleHistoricalQuote();
        quote1.setTimeOpen("2020-08-06T00:00:00.000Z");
        quote1.setTimeClose("2020-08-06T00:00:00.000Z");
        quote1.setTimeHigh("2020-08-06T00:00:00.000Z");
        quote1.setTimeLow("2020-08-06T00:00:00.000Z");
        quote1.setQuote(new Quote());

        CryptoCandleHistoricalQuote quote2 = new CryptoCandleHistoricalQuote();
        quote2.setTimeOpen("2020-08-05T00:00:00.000Z");
        quote2.setTimeClose("2020-08-06T00:00:00.000Z");
        quote2.setTimeHigh("2020-08-05T00:00:00.000Z");
        quote2.setTimeLow("2020-08-05T00:00:00.000Z");
        quote2.setQuote(new Quote());

        cmcCandleDataService.insert("BTC", "USD", List.of(
                quote1,
                quote2
        ));

        assertThat(cmcCandleDataService.find("BTC", "USD")).size().isEqualTo(1);
    }

}
